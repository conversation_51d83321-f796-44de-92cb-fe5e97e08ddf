#!/usr/bin/env python3
"""
Test script to verify GUI is using the updated visualization methods.
"""

import sys
import numpy as np
import cv2
from pathlib import Path

# Add src to path for imports
current_dir = Path(__file__).parent
src_path = current_dir / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def test_gui_method_calls():
    """Test that GUI is calling the correct visualization methods."""
    print("🔍 Testing GUI Method Calls")
    print("=" * 50)
    
    try:
        from photo_center.ui.preview_widget import PreviewWidget
        from photo_center.image_processing.centering import PhotoCenterer, CenteringResult
        from PySide6.QtWidgets import QApplication
        
        # Create QApplication (required for Qt widgets)
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Create preview widget
        preview_widget = PreviewWidget()
        
        # Create test data
        test_image = np.zeros((400, 600, 3), dtype=np.uint8)
        test_image[100:300, 150:450] = (128, 128, 128)
        
        mock_detection = {
            'bbox': [150, 100, 450, 300],
            'confidence': 0.85,
            'keypoints': {
                'nose': (300, 150),
                'left_eye': (280, 140),
                'right_eye': (320, 140)
            }
        }
        
        mock_result = CenteringResult(
            cropped_image=test_image[50:350, 100:500],
            crop_box=(100, 50, 500, 350),
            subject_center=(300, 200),
            target_center=(200, 150),
            confidence=0.75,
            method_used="face_chest_based"
        )
        
        # Set up the preview widget
        preview_widget.set_original_image(test_image)
        preview_widget.set_processed_image(test_image, mock_detection, mock_result)
        
        print("✓ Preview widget created and configured")
        
        # Test the show_detection method directly
        print("\n🎨 Testing show_detection method...")
        
        # Monkey patch the visualization method to verify it's being called
        original_visualize = PhotoCenterer.visualize_original_detection
        
        def patched_visualize(self, image, detection, result):
            print("  ✓ visualize_original_detection called with updated colors!")
            vis_image = original_visualize(self, image, detection, result)
            
            # Check for bright colors
            magenta_pixels = np.sum(np.all(vis_image == [255, 0, 255], axis=2))
            green_pixels = np.sum(np.all(vis_image == [0, 255, 0], axis=2))
            
            print(f"  ✓ Magenta pixels found: {magenta_pixels}")
            print(f"  ✓ Green pixels found: {green_pixels}")
            
            return vis_image
        
        # Apply the patch
        PhotoCenterer.visualize_original_detection = patched_visualize
        
        # Call the GUI method
        preview_widget.show_detection()
        
        print("✓ show_detection method completed")
        
        # Restore original method
        PhotoCenterer.visualize_original_detection = original_visualize
        
        # Clean up
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing GUI method calls: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_color_reference():
    """Create a reference image showing what the colors should look like."""
    print("\n🎨 Creating Color Reference")
    print("=" * 30)
    
    # Create reference image with labeled color patches
    ref_image = np.zeros((300, 800, 3), dtype=np.uint8)
    
    colors = [
        ("Bright Magenta", (255, 0, 255)),
        ("Bright Green", (0, 255, 0)),
        ("Bright Cyan", (255, 255, 0)),
        ("Bright Orange", (0, 165, 255)),
        ("Electric Blue", (255, 255, 0)),
        ("Hot Pink", (147, 20, 255))
    ]
    
    patch_width = 130
    for i, (name, bgr_color) in enumerate(colors):
        x_start = i * patch_width
        x_end = x_start + patch_width - 10
        
        # Fill color patch
        ref_image[:200, x_start:x_end] = bgr_color
        
        # Add label
        cv2.putText(ref_image, name, (x_start + 5, 230), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        cv2.putText(ref_image, f"BGR{bgr_color}", (x_start + 5, 250), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
    
    # Save reference
    cv2.imwrite("color_reference.png", ref_image)
    print("✓ Color reference saved: color_reference.png")
    
    return True

if __name__ == "__main__":
    print("🎯 Testing GUI Refresh and Method Calls")
    print("=" * 60)
    
    success = True
    
    # Test GUI method calls
    if not test_gui_method_calls():
        success = False
    
    # Create color reference
    if not create_color_reference():
        success = False
    
    if success:
        print("\n🎉 GUI refresh tests completed!")
        print("\n💡 If GUI still shows black boxes:")
        print("   1. Restart the GUI application completely")
        print("   2. Clear any Python cache (__pycache__ folders)")
        print("   3. Check that you're clicking 'Show Detection' button")
        print("   4. Compare with color_reference.png to verify expected colors")
    else:
        print("\n❌ Some GUI refresh tests failed")
        sys.exit(1)
