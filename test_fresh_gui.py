#!/usr/bin/env python3
"""
Test script to create a fresh GUI instance and verify colors.
"""

import sys
import numpy as np
import cv2
from pathlib import Path

# Add src to path for imports
current_dir = Path(__file__).parent
src_path = current_dir / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def create_test_gui():
    """Create a simple test GUI to verify detection colors."""
    print("🖼️ Creating Test GUI with Detection Colors")
    print("=" * 50)
    
    try:
        from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
        from PySide6.QtGui import QPixmap, QImage
        from PySide6.QtCore import Qt
        from photo_center.image_processing.centering import PhotoCenterer, CenteringResult
        
        class TestWindow(QMainWindow):
            def __init__(self):
                super().__init__()
                self.setWindowTitle("Detection Color Test")
                self.setGeometry(100, 100, 800, 600)
                
                # Create central widget
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                layout = QVBoxLayout(central_widget)
                
                # Create test button
                self.test_btn = QPushButton("Show Detection with New Colors")
                self.test_btn.clicked.connect(self.show_detection_test)
                layout.addWidget(self.test_btn)
                
                # Create image label
                self.image_label = QLabel()
                self.image_label.setAlignment(Qt.AlignCenter)
                self.image_label.setStyleSheet("border: 1px solid black;")
                layout.addWidget(self.image_label)
                
                # Create info label
                self.info_label = QLabel("Click button to test detection colors")
                layout.addWidget(self.info_label)
            
            def show_detection_test(self):
                """Show detection visualization with new colors."""
                # Create test image
                test_image = np.zeros((400, 600, 3), dtype=np.uint8)
                test_image[100:300, 150:450] = (128, 128, 128)  # Gray rectangle
                
                # Create mock detection
                mock_detection = {
                    'bbox': [150, 100, 450, 300],
                    'confidence': 0.85,
                    'keypoints': {
                        'nose': (300, 150),
                        'left_eye': (280, 140),
                        'right_eye': (320, 140),
                        'left_shoulder': (250, 200),
                        'right_shoulder': (350, 200)
                    }
                }
                
                # Create mock result
                mock_result = CenteringResult(
                    cropped_image=test_image[50:350, 100:500],
                    crop_box=(100, 50, 500, 350),
                    subject_center=(300, 200),
                    target_center=(200, 150),
                    confidence=0.75,
                    method_used="face_chest_based"
                )
                
                # Create visualization
                centerer = PhotoCenterer()
                vis_image = centerer.visualize_original_detection(test_image, mock_detection, mock_result)
                
                # Convert to Qt format for display
                rgb_image = cv2.cvtColor(vis_image, cv2.COLOR_BGR2RGB)
                height, width, channel = rgb_image.shape
                bytes_per_line = 3 * width
                q_image = QImage(rgb_image.data, width, height, bytes_per_line, QImage.Format_RGB888)
                pixmap = QPixmap.fromImage(q_image)
                
                # Scale to fit label
                scaled_pixmap = pixmap.scaled(600, 400, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.image_label.setPixmap(scaled_pixmap)
                
                # Update info
                magenta_pixels = np.sum(np.all(vis_image == [255, 0, 255], axis=2))
                green_pixels = np.sum(np.all(vis_image == [0, 255, 0], axis=2))
                
                self.info_label.setText(
                    f"Detection visualization created! "
                    f"Magenta pixels: {magenta_pixels}, Green pixels: {green_pixels}"
                )
                
                print(f"✓ Detection visualization displayed")
                print(f"  Magenta detection box pixels: {magenta_pixels}")
                print(f"  Green crop area pixels: {green_pixels}")
        
        # Create and show window
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        window = TestWindow()
        window.show()
        
        print("✓ Test GUI created and displayed")
        print("💡 Click the button to see detection with new bright colors!")
        print("💡 You should see:")
        print("   - Bright MAGENTA detection box")
        print("   - Bright GREEN crop area")
        print("   - Bright CYAN subject center")
        print("   - Hot PINK face keypoints")
        
        # Run the application
        app.exec()
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating test GUI: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎯 Fresh GUI Color Test")
    print("=" * 40)
    
    if create_test_gui():
        print("\n🎉 Test GUI completed!")
    else:
        print("\n❌ Test GUI failed")
        sys.exit(1)
