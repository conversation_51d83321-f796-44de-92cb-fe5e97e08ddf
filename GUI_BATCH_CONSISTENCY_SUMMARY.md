# GUI and Batch Processing Consistency

## Problem Solved

Previously, the GUI and batch processing used different algorithms and settings, which meant that what users saw in the GUI preview was not the same as what they got when running batch processing. This inconsistency was confusing and made it impossible for users to reliably preview their batch processing results.

## Solution Implemented

### 1. Unified Configuration System

Added a new configuration option `use_crop_centering` to control which centering algorithm is used:

```yaml
centering:
  use_crop_centering: true  # true for aggressive cropping (perfect centering), false for padding-based centering
```

### 2. Consistent Processing Logic

Both GUI and batch processing now use the exact same logic:

**Before:**
- GUI: Used both PhotoCenterer and CropCenterer, showed PhotoCenterer result, saved PhotoCenterer result
- Batch: Only used CropCenterer, saved Crop<PERSON>enterer result
- **Result:** Different outputs between GUI and batch

**After:**
- Both GUI and batch: Check `config.use_crop_centering` setting
  - If `true`: Use CropCenterer for perfect positioning
  - If `false`: Use PhotoCenterer for padding-based centering
- **Result:** Identical outputs between GUI and batch

### 3. Enhanced GUI Controls

Added new GUI controls to match all configuration options:

- **Use Crop Centering**: Checkbox to toggle between crop centering and regular centering
- **Target X Position**: Control horizontal positioning (0.0 = left, 1.0 = right)
- **Target Y Position**: Control vertical positioning (0.0 = top, 1.0 = bottom)
- **Margin Ratio**: Control margin around subject
- All existing weight controls (Face, Chest, Hip)

### 4. Verification System

Created `test_gui_batch_consistency.py` to verify that GUI and batch processing produce identical results:

```bash
uv run python test_gui_batch_consistency.py
```

This test:
- Loads the same test image
- Processes it using GUI logic
- Processes it using batch logic
- Compares all results (confidence, method, dimensions, crop ratio)
- Reports success/failure

## Key Benefits

1. **Predictable Results**: What you see in the GUI is exactly what you get in batch processing
2. **User Control**: Users can now configure the exact processing method they want
3. **Flexibility**: Can switch between aggressive cropping (perfect centering) and padding-based centering
4. **Consistency**: All settings are respected throughout the entire pipeline

## Configuration Options

### Crop Centering (Recommended)
```yaml
centering:
  use_crop_centering: true
  method: "face_chest_based"
  target_position: [0.5, 0.25]  # Face at 25% from top
```

**Benefits:**
- Perfect subject positioning
- No padding/background
- Consistent framing
- Better for portraits and headshots

### Regular Centering
```yaml
centering:
  use_crop_centering: false
  method: "face_chest_based"
  target_position: [0.5, 0.25]
```

**Benefits:**
- Preserves more of original image
- Less aggressive cropping
- Better when you want to keep background
- More conservative approach

## Usage

### GUI Usage
1. Load an image in the GUI
2. Adjust settings in the Settings panel:
   - Choose centering method
   - Set target position
   - Enable/disable crop centering
   - Adjust weights and margins
3. Click "Process Image" to see preview
4. The preview shows exactly what batch processing will produce
5. Save the result - it will be identical to batch output

### Batch Processing
1. Configure settings in `config.yaml` or via GUI
2. Run batch processing:
   ```bash
   uv run photo-center -i /path/to/images --batch
   ```
3. All images will be processed using the exact same settings as GUI preview

## Testing

The consistency is verified by automated testing:

```bash
# Test with current configuration
uv run python test_gui_batch_consistency.py

# Expected output:
# 🎉 SUCCESS: GUI and batch processing produce identical results!
```

## Migration Notes

- **Default behavior**: Crop centering is enabled by default (same as previous batch behavior)
- **Existing workflows**: No changes needed - batch processing works the same
- **New capability**: Users can now preview batch results in GUI
- **Configuration**: All settings are now configurable via GUI

This ensures that users can confidently use the GUI to define their desired settings and know that batch processing will produce exactly the same results.
