#!/usr/bin/env python3
"""
Test script to verify GUI color display is working correctly.
"""

import sys
import numpy as np
import cv2
from pathlib import Path

# Add src to path for imports
current_dir = Path(__file__).parent
src_path = current_dir / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def test_color_conversion():
    """Test BGR to RGB color conversion."""
    print("🎨 Testing BGR to RGB Color Conversion")
    print("=" * 50)
    
    # Test colors in BGR format (what OpenCV uses)
    test_colors_bgr = {
        'Bright Magenta': (255, 0, 255),
        'Bright Green': (0, 255, 0),
        'Bright Cyan': (255, 255, 0),
        'Bright Orange': (0, 165, 255),
        'Electric Blue': (255, 255, 0),
        'Hot Pink': (147, 20, 255)
    }
    
    print("BGR colors (what we specify in code):")
    for name, bgr in test_colors_bgr.items():
        # Convert BGR to RGB (what GUI displays)
        rgb = (bgr[2], bgr[1], bgr[0])  # Swap R and B channels
        print(f"  {name}: BGR{bgr} → RGB{rgb}")
    
    # Create test image with color patches
    test_image = np.zeros((200, 600, 3), dtype=np.uint8)
    
    x_pos = 0
    for name, bgr_color in test_colors_bgr.items():
        # Draw color patch in BGR
        test_image[:, x_pos:x_pos+100] = bgr_color
        x_pos += 100
    
    # Save BGR version
    cv2.imwrite("test_colors_bgr.png", test_image)
    print(f"\n✓ BGR test image saved: test_colors_bgr.png")
    
    # Convert to RGB and save
    rgb_image = cv2.cvtColor(test_image, cv2.COLOR_BGR2RGB)
    cv2.imwrite("test_colors_rgb.png", cv2.cvtColor(rgb_image, cv2.COLOR_RGB2BGR))
    print(f"✓ RGB test image saved: test_colors_rgb.png")
    
    return True

def test_actual_gui_visualization():
    """Test the actual GUI visualization with a real image."""
    print("\n🖼️ Testing Actual GUI Visualization")
    print("=" * 50)
    
    try:
        from photo_center.image_processing.centering import PhotoCenterer, CenteringResult
        from photo_center.models.unified_detector import UnifiedHumanDetector
        from photo_center.utils.config import Config
        
        # Load a real test image
        test_image_path = Path("test_photos/_SPC5194.NEF")
        if not test_image_path.exists():
            print("❌ Test image not found, creating synthetic image")
            # Create synthetic image
            test_image = np.zeros((600, 800, 3), dtype=np.uint8)
            test_image[200:400, 300:500] = (128, 128, 128)
            
            # Create mock detection
            mock_detection = {
                'bbox': [300, 200, 500, 400],
                'confidence': 0.85,
                'keypoints': {
                    'nose': (400, 250),
                    'left_eye': (380, 240),
                    'right_eye': (420, 240)
                }
            }
            
            mock_result = CenteringResult(
                cropped_image=test_image[150:450, 250:550],
                crop_box=(250, 150, 550, 450),
                subject_center=(400, 300),
                target_center=(300, 225),
                confidence=0.75,
                method_used="face_chest_based"
            )
            
        else:
            print(f"✓ Loading real test image: {test_image_path}")
            
            # Initialize components
            config = Config()
            detector = UnifiedHumanDetector(config)
            centerer = PhotoCenterer(config)
            
            # Load and process real image
            from photo_center.image_processing.raw_processor import RawProcessor
            raw_processor = RawProcessor(config)
            test_image = raw_processor.load_image(test_image_path)
            
            # Detect humans
            detections = detector.detect_humans(test_image)
            if not detections:
                print("❌ No detections found in real image")
                return False
            
            mock_detection = detections[0]
            
            # Create centering result
            mock_result = centerer.center_subject(test_image, mock_detection)
        
        # Test the visualization
        centerer = PhotoCenterer()
        vis_image = centerer.visualize_original_detection(test_image, mock_detection, mock_result)
        
        # Save the visualization
        cv2.imwrite("actual_gui_visualization.png", vis_image)
        print(f"✓ Actual GUI visualization saved: actual_gui_visualization.png")
        
        # Analyze colors in the visualization
        print("\n📊 Analyzing colors in visualization:")
        
        # Check for specific color values
        color_checks = {
            'Bright Magenta (255,0,255)': (255, 0, 255),
            'Bright Green (0,255,0)': (0, 255, 0),
            'Bright Cyan (255,255,0)': (255, 255, 0),
            'Hot Pink (147,20,255)': (147, 20, 255)
        }
        
        for color_name, bgr_color in color_checks.items():
            mask = np.all(vis_image == bgr_color, axis=2)
            pixel_count = np.sum(mask)
            print(f"  {color_name}: {pixel_count} pixels")
        
        # Check for any non-black colored pixels
        non_black_mask = np.any(vis_image != [0, 0, 0], axis=2)
        non_black_pixels = np.sum(non_black_mask)
        print(f"  Total non-black pixels: {non_black_pixels}")
        
        # Check for any bright pixels (high intensity)
        bright_mask = np.any(vis_image > 200, axis=2)
        bright_pixels = np.sum(bright_mask)
        print(f"  Bright pixels (>200 intensity): {bright_pixels}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing GUI visualization: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎯 Testing GUI Color Display")
    print("=" * 60)
    
    success = True
    
    # Test color conversion
    if not test_color_conversion():
        success = False
    
    # Test actual GUI visualization
    if not test_actual_gui_visualization():
        success = False
    
    if success:
        print("\n🎉 GUI color display tests completed!")
        print("Check the saved images to verify colors are displaying correctly.")
    else:
        print("\n❌ Some GUI color display tests failed")
        sys.exit(1)
