#!/usr/bin/env python3
"""
Test script to verify that centering detection (not orientation detection) is displayed.
"""

import sys
import numpy as np
import cv2
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_centering_detection_display():
    """Test that the GUI shows centering detection with proper labels."""
    print("🎯 Testing Centering Detection Display")
    print("=" * 50)
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.photo_center.ui.preview_widget import PreviewWidget
        from src.photo_center.image_processing.centering import CenteringResult
        
        # Create QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Create preview widget
        preview_widget = PreviewWidget()
        
        # Create test scenario: rotated image with centering detection
        original_image = np.zeros((400, 600, 3), dtype=np.uint8)
        original_image[100:300, 150:450] = (64, 64, 64)  # Gray rectangle
        
        # Simulate rotated image (this would be the processing_image after rotation)
        rotated_image = cv2.rotate(original_image, cv2.ROTATE_90_CLOCKWISE)
        
        # This is the CENTERING detection (post-rotation, used for actual centering)
        centering_detection = {
            'bbox': [100, 150, 300, 450],
            'confidence': 0.92,  # High confidence centering detection
            'keypoints': {
                'nose': (200, 250),
                'left_eye': (180, 240),
                'right_eye': (220, 240),
                'left_shoulder': (160, 300),
                'right_shoulder': (240, 300)
            }
        }
        
        # Centering result
        centering_result = CenteringResult(
            cropped_image=rotated_image[50:350, 50:350],
            crop_box=(50, 50, 350, 350),
            subject_center=(200, 250),
            target_center=(175, 200),
            confidence=0.88,
            method_used="face_chest_based"
        )
        
        print("✓ Test data created for centering detection scenario")
        
        # Test with different models
        models_to_test = ["YOLO11", "MEDIAPIPE", "AUTO"]
        
        for model_name in models_to_test:
            print(f"\n🔍 Testing with {model_name} model...")
            
            # Capture visualization
            captured_image = None
            original_set_image = preview_widget.image_label.set_image
            
            def capture_image(image):
                nonlocal captured_image
                captured_image = image.copy()
                return original_set_image(image)
            
            preview_widget.image_label.set_image = capture_image
            
            # Set up preview widget with centering detection
            preview_widget.set_original_image(original_image)
            preview_widget.set_processed_image(
                centering_result.cropped_image,
                centering_detection,  # This is the CENTERING detection
                centering_result,
                rotated_image,  # Processing image (rotated)
                model_name.lower()  # Model used for centering
            )
            
            # Call show_detection
            preview_widget.show_detection()
            
            if captured_image is not None:
                # Save result
                filename = f"centering_detection_{model_name.lower()}.png"
                cv2.imwrite(filename, captured_image)
                
                # Analyze the visualization
                magenta_pixels = np.sum(np.all(captured_image == [255, 0, 255], axis=2))
                green_pixels = np.sum(np.all(captured_image == [0, 255, 0], axis=2))
                hot_pink_pixels = np.sum(np.all(captured_image == [255, 20, 147], axis=2))
                
                print(f"   💾 Saved: {filename}")
                print(f"   📊 Colors: {magenta_pixels} magenta, {green_pixels} green, {hot_pink_pixels} hot pink")
                
                # Check for centering label in the image
                # Convert to grayscale to check for text
                gray = cv2.cvtColor(captured_image, cv2.COLOR_BGR2GRAY)
                has_text_areas = np.sum(gray == 0) > 1000  # Black text backgrounds
                
                if has_text_areas:
                    print(f"   ✅ Contains text labels (centering detection info)")
                else:
                    print(f"   ⚠️ May be missing text labels")
                
                if magenta_pixels > 0 and green_pixels > 0:
                    print(f"   ✅ {model_name} centering detection visualization working!")
                else:
                    print(f"   ❌ {model_name} centering detection visualization has issues")
        
        # Clean up
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing centering detection display: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_legend_clarity():
    """Test that the legend makes it clear this is centering detection."""
    print("\n📋 Testing Legend Clarity")
    print("=" * 30)
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.photo_center.ui.preview_widget import PreviewWidget
        from src.photo_center.image_processing.centering import CenteringResult
        
        # Create QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Create preview widget
        preview_widget = PreviewWidget()
        
        # Create test data
        test_image = np.zeros((500, 700, 3), dtype=np.uint8)
        test_image[150:350, 200:500] = (80, 80, 80)
        
        detection = {
            'bbox': [200, 150, 500, 350],
            'confidence': 0.95,
            'keypoints': {
                'nose': (350, 200),
                'left_eye': (330, 190),
                'right_eye': (370, 190)
            }
        }
        
        result = CenteringResult(
            cropped_image=test_image[100:400, 150:550],
            crop_box=(150, 100, 550, 400),
            subject_center=(350, 250),
            target_center=(300, 200),
            confidence=0.90,
            method_used="face_chest_based"
        )
        
        # Test with clear model name
        captured_image = None
        original_set_image = preview_widget.image_label.set_image
        
        def capture_image(image):
            nonlocal captured_image
            captured_image = image.copy()
            return original_set_image(image)
        
        preview_widget.image_label.set_image = capture_image
        
        # Set up with YOLO model
        preview_widget.set_original_image(test_image)
        preview_widget.set_processed_image(
            result.cropped_image,
            detection,
            result,
            test_image,
            "yolo11"  # Clear model identification
        )
        
        # Call show_detection
        preview_widget.show_detection()
        
        if captured_image is not None:
            cv2.imwrite("centering_detection_legend_test.png", captured_image)
            print("✅ Legend test image saved: centering_detection_legend_test.png")
            
            # Check for legend elements
            legend_area = captured_image[-120:, :200]  # Bottom left where legend should be
            has_legend = np.sum(legend_area == 0) > 500  # Black background areas
            
            if has_legend:
                print("✅ Legend area detected in visualization")
            else:
                print("⚠️ Legend may not be visible")
        
        # Clean up
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing legend clarity: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎯 Testing Centering Detection Display")
    print("=" * 60)
    
    success = True
    
    # Test centering detection display
    if not test_centering_detection_display():
        success = False
    
    # Test legend clarity
    if not test_legend_clarity():
        success = False
    
    if success:
        print("\n🎉 Centering Detection Display Tests Completed!")
        print("\n💡 Key Points:")
        print("   • GUI now shows CENTERING detection (post-rotation)")
        print("   • NOT orientation detection (pre-rotation)")
        print("   • Labels clearly indicate 'CENTERING (MODEL)'")
        print("   • Legend explains what each color represents")
        print("   • Model name (YOLO11/MEDIAPIPE) is displayed")
        print("\n📋 What You'll See in GUI:")
        print("   • Bright magenta detection box for centering")
        print("   • Label: 'CENTERING (YOLO11): 0.92' or similar")
        print("   • Legend showing this is centering detection")
        print("   • All coordinates match the final processed image")
    else:
        print("\n❌ Some centering detection display tests failed")
        sys.exit(1)
