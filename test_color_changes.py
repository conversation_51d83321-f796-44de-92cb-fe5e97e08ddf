#!/usr/bin/env python3
"""
Test script to verify the updated detection colors are more visible.
"""

import sys
import numpy as np
import cv2
from pathlib import Path

# Add src to path for imports
current_dir = Path(__file__).parent
src_path = current_dir / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def test_color_visibility():
    """Test the new bright colors for detection visualization."""
    print("🎨 Testing Updated Detection Colors")
    print("=" * 50)
    
    # Test the new color palette
    new_colors = [
        (255, 0, 255),    # Bright Magenta
        (0, 255, 0),      # <PERSON>  
        (0, 255, 255),    # <PERSON>
        (0, 165, 255),    # <PERSON> Orange
        (255, 255, 0),    # Electric Blue
        (255, 20, 147)    # Hot Pink
    ]
    
    # Test keypoint colors
    keypoint_colors = {
        'face': (255, 20, 147),    # Hot Pink for face - highly visible
        'shoulder': (0, 255, 0),   # Bright Green for shoulders
        'hip': (0, 165, 255),      # Bright Orange for hips
        'other': (0, 255, 255)     # <PERSON> for others
    }
    
    print("✓ New detection box colors:")
    for i, color in enumerate(new_colors):
        print(f"   Color {i+1}: BGR{color}")
    
    print("\n✓ New keypoint colors:")
    for part, color in keypoint_colors.items():
        print(f"   {part.capitalize()}: BGR{color}")
    
    # Create a test image with various backgrounds to test visibility
    test_image = np.zeros((600, 800, 3), dtype=np.uint8)
    
    # Add different background regions
    test_image[0:200, :] = (50, 50, 50)      # Dark background
    test_image[200:400, :] = (128, 128, 128)  # Medium background  
    test_image[400:600, :] = (200, 200, 200)  # Light background
    
    # Test drawing boxes with new colors
    for i, color in enumerate(new_colors):
        x = 50 + i * 120
        y = 50
        cv2.rectangle(test_image, (x, y), (x + 100, y + 150), color, 4)
        cv2.putText(test_image, f"Box {i+1}", (x, y - 10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
    
    # Test keypoint colors
    y_positions = [250, 350, 450, 550]
    for i, (part, color) in enumerate(keypoint_colors.items()):
        for j in range(6):  # Test on different backgrounds
            x = 75 + j * 120
            y = y_positions[i]
            cv2.circle(test_image, (x, y), 8, color, -1)
            cv2.circle(test_image, (x, y), 10, (255, 255, 255), 2)
    
    print(f"\n✓ Created test visualization: {test_image.shape}")
    
    # Save test image
    output_path = "color_visibility_test.png"
    cv2.imwrite(output_path, test_image)
    print(f"✓ Saved test image to: {output_path}")
    
    return True

def test_import_detection_modules():
    """Test that the detection modules can be imported with new colors."""
    print("\n🔧 Testing Module Imports")
    print("=" * 30)
    
    try:
        from photo_center.models.human_detector import HumanDetector
        print("✓ HumanDetector imported successfully")
        
        from photo_center.image_processing.centering import PhotoCenterer
        print("✓ PhotoCenterer imported successfully")
        
        from photo_center.ui.preview_widget import PreviewWidget
        print("✓ PreviewWidget imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

if __name__ == "__main__":
    print("🎯 Testing Detection Color Improvements")
    print("=" * 60)
    
    success = True
    
    # Test color visibility
    if not test_color_visibility():
        success = False
    
    # Test module imports
    if not test_import_detection_modules():
        success = False
    
    if success:
        print("\n🎉 All color update tests passed!")
        print("Detection boxes should now be much more visible with:")
        print("   • Bright Magenta, Green, Cyan, Orange, Blue, and Hot Pink")
        print("   • Hot Pink for face keypoints (highly visible)")
        print("   • Bright colors for all detection elements")
    else:
        print("\n❌ Some tests failed")
        sys.exit(1)
