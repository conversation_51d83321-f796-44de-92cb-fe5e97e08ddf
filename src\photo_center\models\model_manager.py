"""Model manager for handling different human detection models."""

import numpy as np
from typing import List, Dict, Any, Optional, Union
from enum import Enum

from ..utils.logger import get_logger
from ..utils.config import Config
from .human_detector import HumanDetector
from .openpose_detector import OpenPoseDetector


class ModelType(Enum):
    """Available model types."""
    YOLO = "yolo"
    OPENPOSE = "openpose"
    AUTO = "auto"


class ModelManager:
    """Manages different human detection models and provides unified interface."""
    
    def __init__(self, config: Optional[Config] = None):
        """Initialize model manager.
        
        Args:
            config: Configuration object. If None, creates default config.
        """
        self.config = config or Config()
        self.logger = get_logger(__name__)
        
        # Model instances
        self._yolo_detector = None
        self._openpose_detector = None
        self._current_model_type = None
        
        # Load initial model based on config
        self._initialize_models()
    
    def _initialize_models(self) -> None:
        """Initialize models based on configuration."""
        model_type = self.config.get('models.detection_model', 'yolo')
        
        try:
            if model_type == ModelType.AUTO.value:
                # Auto mode: try OpenPose first, fallback to YOLO
                self._current_model_type = ModelType.AUTO
                self.logger.info("Auto model selection enabled")
            elif model_type == ModelType.OPENPOSE.value:
                self._current_model_type = ModelType.OPENPOSE
                self._load_openpose()
            else:  # Default to YOLO
                self._current_model_type = ModelType.YOLO
                self._load_yolo()
                
        except Exception as e:
            self.logger.error(f"Error initializing models: {e}")
            # Fallback to YOLO if there's an issue
            self._current_model_type = ModelType.YOLO
            self._load_yolo()
    
    def _load_yolo(self) -> None:
        """Load YOLO detector."""
        if self._yolo_detector is None:
            self.logger.info("Loading YOLO detector...")
            self._yolo_detector = HumanDetector(self.config)
            self.logger.info("YOLO detector loaded successfully")
    
    def _load_openpose(self) -> None:
        """Load OpenPose detector."""
        if self._openpose_detector is None:
            self.logger.info("Loading OpenPose detector...")
            self._openpose_detector = OpenPoseDetector(self.config)
            self.logger.info("OpenPose detector loaded successfully")
    
    def detect_humans(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """Detect humans in image using the configured model.

        Args:
            image: Input image as numpy array (BGR format, 8-bit or 16-bit)

        Returns:
            List of detection dictionaries
        """
        # Prepare image for inference (convert to 8-bit if needed)
        from ..image_processing.raw_processor import RawProcessor
        raw_processor = RawProcessor(self.config)
        inference_image = raw_processor.prepare_for_inference(image)

        if self._current_model_type == ModelType.AUTO:
            return self._detect_with_auto_selection(inference_image)
        elif self._current_model_type == ModelType.OPENPOSE:
            return self._detect_with_openpose(inference_image)
        else:  # YOLO
            return self._detect_with_yolo(inference_image)
    
    def _detect_with_auto_selection(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """Detect humans using automatic model selection.
        
        Tries OpenPose first for better keypoint accuracy, falls back to YOLO.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            List of detection dictionaries
        """
        try:
            # Try OpenPose first
            self._load_openpose()
            detections = self._openpose_detector.detect_humans(image)
            
            # If OpenPose finds good detections, use them
            if detections and any(d['confidence'] > 0.5 for d in detections):
                self.logger.debug("Using OpenPose detections (auto mode)")
                return detections
            
        except Exception as e:
            self.logger.warning(f"OpenPose detection failed in auto mode: {e}")
        
        # Fallback to YOLO
        try:
            self._load_yolo()
            detections = self._yolo_detector.detect_humans(image)
            self.logger.debug("Using YOLO detections (auto mode fallback)")
            return detections
            
        except Exception as e:
            self.logger.error(f"Both OpenPose and YOLO failed: {e}")
            return []
    
    def _detect_with_openpose(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """Detect humans using OpenPose."""
        self._load_openpose()
        return self._openpose_detector.detect_humans(image)
    
    def _detect_with_yolo(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """Detect humans using YOLO."""
        self._load_yolo()
        return self._yolo_detector.detect_humans(image)
    
    def get_best_detection(self, detections: List[Dict[str, Any]], image_shape: Optional[tuple] = None) -> Dict[str, Any]:
        """Get the best detection from a list of detections.
        
        Args:
            detections: List of detection dictionaries
            image_shape: Optional image shape (height, width) for better center calculation
            
        Returns:
            Best detection dictionary
        """
        if not detections:
            raise ValueError("No detections provided")
        
        if len(detections) == 1:
            return detections[0]
        
        # Use model-specific selection logic
        if self._current_model_type == ModelType.OPENPOSE or (
            self._current_model_type == ModelType.AUTO and 
            self._openpose_detector is not None and 
            detections[0].get('raw_landmarks') is not None
        ):
            # Use OpenPose's center-person selection for graduation photos
            return self._select_best_for_graduation_photos(detections, image_shape)
        else:
            # Use YOLO's selection logic
            self._load_yolo()
            return self._yolo_detector.get_best_detection(detections)
    
    def _select_best_for_graduation_photos(self, detections: List[Dict[str, Any]], image_shape: Optional[tuple] = None) -> Dict[str, Any]:
        """Select best detection optimized for graduation photos.
        
        Prioritizes the most centered person with good face/upper body visibility.
        
        Args:
            detections: List of detection dictionaries
            image_shape: Optional image shape (height, width)
            
        Returns:
            Best detection for graduation photos
        """
        if image_shape:
            height, width = image_shape
            image_center_x = width / 2
            image_center_y = height / 2
        else:
            # Use detection centers to estimate image size
            all_centers = [d['center'] for d in detections]
            max_x = max(c[0] for c in all_centers)
            max_y = max(c[1] for c in all_centers)
            image_center_x = max_x
            image_center_y = max_y
        
        def graduation_score(detection):
            """Calculate graduation photo suitability score."""
            center_x, center_y = detection['center']
            keypoints = detection.get('keypoints', {})
            
            # Distance from image center (lower is better)
            center_distance = np.sqrt(
                (center_x - image_center_x) ** 2 + 
                (center_y - image_center_y) ** 2
            )
            
            # Normalize distance score (closer to center = higher score)
            max_distance = np.sqrt(image_center_x**2 + image_center_y**2)
            center_score = 1.0 - (center_distance / max_distance)
            
            # Face visibility score (important for graduation photos)
            face_keypoints = ['nose', 'left_eye', 'right_eye', 'left_ear', 'right_ear']
            face_visible = sum(1 for kp in face_keypoints if kp in keypoints)
            face_score = face_visible / len(face_keypoints)
            
            # Upper body visibility score
            upper_body_keypoints = ['left_shoulder', 'right_shoulder']
            upper_body_visible = sum(1 for kp in upper_body_keypoints if kp in keypoints)
            upper_body_score = upper_body_visible / len(upper_body_keypoints)
            
            # Size score (prefer reasonably sized detections, not too small/large)
            bbox = detection['bbox']
            bbox_area = (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])
            if image_shape:
                image_area = width * height
                size_ratio = bbox_area / image_area
                # Prefer detections that are 10-60% of image area
                if 0.1 <= size_ratio <= 0.6:
                    size_score = 1.0
                elif size_ratio < 0.1:
                    size_score = size_ratio / 0.1  # Penalize too small
                else:
                    size_score = max(0.1, 1.0 - (size_ratio - 0.6) / 0.4)  # Penalize too large
            else:
                size_score = 0.5  # Neutral when we can't calculate
            
            # Combine scores with weights optimized for graduation photos
            total_score = (
                center_score * 0.4 +        # Most important: centered position
                face_score * 0.3 +          # Face visibility crucial
                detection['confidence'] * 0.2 +  # Detection quality
                upper_body_score * 0.05 +   # Upper body visibility
                size_score * 0.05           # Reasonable size
            )
            
            return total_score
        
        # Find detection with highest graduation score
        best_detection = max(detections, key=graduation_score)
        
        self.logger.debug(f"Selected best graduation photo subject from {len(detections)} detections")
        self.logger.debug(f"Selected detection confidence: {best_detection['confidence']:.3f}")
        
        return best_detection

    def get_two_best_detections(self, detections: List[Dict[str, Any]], image_shape: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """Get the two best detections from a list of detections for two-person centering.

        Args:
            detections: List of detection dictionaries
            image_shape: Optional image shape (height, width) for better center calculation

        Returns:
            List of the two best detection dictionaries (or all detections if less than 2)
        """
        if not detections:
            return []

        if len(detections) <= 2:
            return detections

        # For two-person mode, we want the two most centered people
        if image_shape:
            height, width = image_shape
            image_center_x = width / 2
            image_center_y = height / 2
        else:
            # Use detection centers to estimate image size
            all_centers = [d['center'] for d in detections]
            max_x = max(c[0] for c in all_centers)
            max_y = max(c[1] for c in all_centers)
            image_center_x = max_x / 2
            image_center_y = max_y / 2

        # Calculate distance from image center for each detection
        def distance_from_center(detection):
            center = detection.get('center', (0, 0))
            return np.sqrt((center[0] - image_center_x)**2 + (center[1] - image_center_y)**2)

        # Sort detections by distance from center and take the two closest
        sorted_detections = sorted(detections, key=distance_from_center)
        two_best = sorted_detections[:2]

        self.logger.debug(f"Selected two most centered people from {len(detections)} detections")
        self.logger.debug(f"Selected detections at: {[d['center'] for d in two_best]}")

        return two_best

    def get_current_model_type(self) -> ModelType:
        """Get the currently active model type."""
        return self._current_model_type
    
    def switch_model(self, model_type: Union[ModelType, str]) -> None:
        """Switch to a different model type.
        
        Args:
            model_type: Model type to switch to
        """
        if isinstance(model_type, str):
            model_type = ModelType(model_type)
        
        self._current_model_type = model_type
        self.config.set('models.detection_model', model_type.value)
        
        self.logger.info(f"Switched to model type: {model_type.value}")
    
    def get_available_models(self) -> List[str]:
        """Get list of available model types."""
        return [model.value for model in ModelType]
