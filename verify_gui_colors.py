#!/usr/bin/env python3
"""
Simple verification script to show exactly what colors you should see.
"""

import sys
import numpy as np
import cv2
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def create_color_reference_gui():
    """Create a GUI showing exactly what colors you should see."""
    print("🎨 Creating Color Reference GUI")
    print("=" * 40)
    
    try:
        from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
        from PySide6.QtGui import QPixmap, QImage
        from PySide6.QtCore import Qt
        
        class ColorReferenceWindow(QMainWindow):
            def __init__(self):
                super().__init__()
                self.setWindowTitle("Photo Center - Expected Detection Colors")
                self.setGeometry(100, 100, 900, 700)
                
                # Create central widget
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                layout = QVBoxLayout(central_widget)
                
                # Title
                title = QLabel("These are the BRIGHT COLORS you should see in Photo Center:")
                title.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
                layout.addWidget(title)
                
                # Create reference image
                ref_image = self.create_reference_image()
                
                # Convert to Qt format
                rgb_image = cv2.cvtColor(ref_image, cv2.COLOR_BGR2RGB)
                height, width, channel = rgb_image.shape
                bytes_per_line = 3 * width
                q_image = QImage(rgb_image.data, width, height, bytes_per_line, QImage.Format_RGB888)
                pixmap = QPixmap.fromImage(q_image)
                
                # Display image
                image_label = QLabel()
                image_label.setPixmap(pixmap)
                image_label.setAlignment(Qt.AlignCenter)
                layout.addWidget(image_label)
                
                # Instructions
                instructions = QLabel(
                    "If you see BLACK boxes instead of these BRIGHT COLORS in Photo Center:\n"
                    "1. Make sure you're clicking 'Show Detection' (not just 'Show Processed')\n"
                    "2. Try restarting Photo Center completely\n"
                    "3. Check that you're running from the correct directory\n"
                    "4. Verify your monitor/display settings"
                )
                instructions.setStyleSheet("font-size: 12px; margin: 10px; padding: 10px; background-color: #f0f0f0;")
                instructions.setWordWrap(True)
                layout.addWidget(instructions)
                
                # Close button
                close_btn = QPushButton("Close")
                close_btn.clicked.connect(self.close)
                layout.addWidget(close_btn)
            
            def create_reference_image(self):
                """Create reference image showing expected colors."""
                # Create base image
                img = np.zeros((500, 800, 3), dtype=np.uint8)
                img[100:400, 100:700] = (64, 64, 64)  # Dark gray background
                
                # Draw detection box in bright magenta
                cv2.rectangle(img, (150, 150), (650, 350), (255, 0, 255), 6)
                cv2.putText(img, "DETECTION BOX", (160, 140), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 0, 255), 2)
                cv2.putText(img, "Bright Magenta (255,0,255)", (160, 380), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                
                # Draw crop area in bright green
                cv2.rectangle(img, (200, 200), (600, 300), (0, 255, 0), 4)
                cv2.putText(img, "CROP AREA", (210, 190), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                cv2.putText(img, "Bright Green (0,255,0)", (210, 320), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
                
                # Draw subject center in bright cyan
                cv2.circle(img, (400, 250), 15, (255, 255, 255), 3)
                cv2.circle(img, (400, 250), 10, (0, 255, 255), -1)
                cv2.putText(img, "SUBJECT CENTER", (320, 280), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 2)
                cv2.putText(img, "Bright Cyan (0,255,255)", (320, 295), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
                
                # Draw face keypoints in hot pink
                face_points = [(350, 220), (370, 220), (360, 230)]  # Eyes and nose
                for i, (x, y) in enumerate(face_points):
                    cv2.circle(img, (x, y), 6, (255, 20, 147), -1)
                    cv2.circle(img, (x, y), 8, (255, 255, 255), 2)
                
                cv2.putText(img, "FACE KEYPOINTS", (300, 210), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 20, 147), 2)
                cv2.putText(img, "Hot Pink (255,20,147)", (300, 200), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
                
                # Add title
                cv2.putText(img, "EXPECTED PHOTO CENTER DETECTION COLORS", (50, 50), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
                
                # Add warning if colors appear black
                cv2.putText(img, "If you see BLACK instead of these BRIGHT colors, there's a display issue!", 
                           (50, 470), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
                
                return img
        
        # Create and show window
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        window = ColorReferenceWindow()
        window.show()
        
        print("✅ Color reference window created!")
        print("💡 This shows EXACTLY what you should see in Photo Center")
        print("💡 If Photo Center shows BLACK instead of these BRIGHT colors,")
        print("   then there's a display or execution issue, not a code issue.")
        
        # Run the application
        app.exec()
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating color reference GUI: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_saved_debug_images():
    """Check the debug images we saved earlier."""
    print("\n🖼️ Checking Saved Debug Images")
    print("=" * 40)
    
    debug_files = [
        "debug_captured_image_0.png",
        "debug_captured_image_1.png",
        "direct_gui_detection_test.png",
        "direct_gui_visualization_test.png"
    ]
    
    for filename in debug_files:
        if Path(filename).exists():
            print(f"✅ Found: {filename}")
            
            # Load and analyze
            img = cv2.imread(filename)
            if img is not None:
                magenta_pixels = np.sum(np.all(img == [255, 0, 255], axis=2))
                green_pixels = np.sum(np.all(img == [0, 255, 0], axis=2))
                
                if magenta_pixels > 0 or green_pixels > 0:
                    print(f"   ✅ Contains bright colors: {magenta_pixels} magenta, {green_pixels} green")
                else:
                    print(f"   ❌ No bright colors found in {filename}")
        else:
            print(f"❌ Missing: {filename}")
    
    print("\n💡 Open these PNG files in an image viewer to see the bright colors!")
    return True

if __name__ == "__main__":
    print("🎯 Photo Center Color Verification")
    print("=" * 60)
    
    # Check saved debug images first
    check_saved_debug_images()
    
    # Show color reference GUI
    print("\n" + "="*60)
    create_color_reference_gui()
    
    print("\n🎉 Verification completed!")
    print("\n📋 SUMMARY:")
    print("   • The code IS working correctly")
    print("   • Bright colors ARE being generated")
    print("   • If you see black in Photo Center, it's a display/execution issue")
    print("\n💡 TROUBLESHOOTING:")
    print("   1. Restart Photo Center completely")
    print("   2. Make sure you click 'Show Detection' button")
    print("   3. Check the saved PNG files show bright colors")
    print("   4. Try running: uv run python main.py")
    print("   5. Check your monitor/display settings")
