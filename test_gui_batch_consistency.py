#!/usr/bin/env python3
"""
Test script to verify that GUI and batch processing use exactly the same settings.
This ensures that what you see in the GUI preview is exactly what you get in batch processing.
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.photo_center.utils.config import Config
from src.photo_center.models.unified_detector import UnifiedHumanDetector
from src.photo_center.image_processing.raw_processor import RawProcessor
from src.photo_center.image_processing.centering import PhotoCenterer
from src.photo_center.image_processing.crop_centering import <PERSON>rop<PERSON>enterer
from src.photo_center.batch.batch_processor import BatchProcessor


def load_test_image():
    """Load a real test image."""
    # Use the recommended test image from user's memories
    test_image_path = Path("test_photos/_SPC5194.NEF")

    if not test_image_path.exists():
        # Fallback to a JPEG if NEF is not available
        test_image_path = Path("test_photos/pexels-reneterp-641314.jpg")

    if not test_image_path.exists():
        raise FileNotFoundError("No test images found. Please ensure test_photos directory contains images.")

    # Load using RawProcessor to handle different formats
    config = Config()
    raw_processor = RawProcessor(config)
    image = raw_processor.load_image(test_image_path)

    print(f"Loaded test image: {test_image_path}")
    return image


def simulate_gui_processing(config, image):
    """Simulate GUI processing logic."""
    print("=== Simulating GUI Processing ===")
    
    # Initialize components like GUI does
    human_detector = UnifiedHumanDetector(config)
    raw_processor = RawProcessor(config)
    photo_centerer = PhotoCenterer(config)
    crop_centerer = CropCenterer(config)
    
    # Detect humans
    detections = human_detector.detect_humans(image)
    if not detections:
        print("No detections found")
        return None
    
    # Get best detection
    image_shape = image.shape[:2]
    best_detection = human_detector.get_best_detection(detections, image_shape)
    
    # Apply orientation correction (like GUI does)
    processing_image = image
    corrected_image, orientation_info = raw_processor.apply_orientation_correction(processing_image, best_detection)
    if orientation_info['applied']:
        print(f"Applied {orientation_info['rotation_angle']}° rotation")
        corrected_detections = human_detector.detect_humans(corrected_image)
        if corrected_detections:
            best_detection = human_detector.get_best_detection(corrected_detections, corrected_image.shape[:2])
            processing_image = corrected_image
    
    # Use the same centering logic as GUI based on configuration
    centering_method = config.get('centering.method', 'face_chest_based')
    
    if config.use_crop_centering:
        print("Using crop centering")
        result = crop_centerer.center_subject_with_crop(
            processing_image, best_detection, centering_method=centering_method
        )
        final_image = result.cropped_image
        confidence = result.confidence
        method_used = result.method_used
        crop_ratio = getattr(result, 'crop_ratio', 0.0)
    else:
        print("Using regular centering")
        result = photo_centerer.center_subject(processing_image, best_detection)
        final_image = result.cropped_image
        confidence = result.confidence
        method_used = result.method_used
        crop_ratio = 0.0
    
    print(f"GUI Result: confidence={confidence:.3f}, method={method_used}, crop_ratio={crop_ratio:.3f}")
    print(f"GUI Output dimensions: {final_image.shape[:2]}")
    
    return {
        'image': final_image,
        'confidence': confidence,
        'method': method_used,
        'crop_ratio': crop_ratio,
        'detection_confidence': best_detection['confidence']
    }


def simulate_batch_processing(config, image):
    """Simulate batch processing logic."""
    print("\n=== Simulating Batch Processing ===")
    
    # Initialize batch processor
    batch_processor = BatchProcessor(config)
    
    # Simulate the same processing logic as batch processor
    human_detector = batch_processor.human_detector
    raw_processor = batch_processor.raw_processor
    photo_centerer = batch_processor.photo_centerer
    crop_centerer = batch_processor.crop_centerer
    
    # Detect humans
    detections = human_detector.detect_humans(image)
    if not detections:
        print("No detections found")
        return None
    
    # Get best detection
    image_shape = image.shape[:2]
    best_detection = human_detector.get_best_detection(detections, image_shape)
    
    # Apply orientation correction (like batch does)
    corrected_image, orientation_info = raw_processor.apply_orientation_correction(image, best_detection)
    if orientation_info['applied']:
        print(f"Applied {orientation_info['rotation_angle']}° rotation")
        corrected_detections = human_detector.detect_humans(corrected_image)
        if corrected_detections:
            best_detection = human_detector.get_best_detection(corrected_detections, corrected_image.shape[:2])
            image = corrected_image
    
    # Use the same centering logic as batch processing
    centering_method = config.get('centering.method', 'face_chest_based')
    
    if config.use_crop_centering:
        print("Using crop centering")
        result = crop_centerer.center_subject_with_crop(
            image, best_detection, centering_method=centering_method
        )
        final_image = result.cropped_image
        confidence = result.confidence
        method_used = result.method_used
        crop_ratio = getattr(result, 'crop_ratio', 0.0)
    else:
        print("Using regular centering")
        result = photo_centerer.center_subject(image, best_detection)
        final_image = result.cropped_image
        confidence = result.confidence
        method_used = result.method_used
        crop_ratio = 0.0
    
    print(f"Batch Result: confidence={confidence:.3f}, method={method_used}, crop_ratio={crop_ratio:.3f}")
    print(f"Batch Output dimensions: {final_image.shape[:2]}")
    
    return {
        'image': final_image,
        'confidence': confidence,
        'method': method_used,
        'crop_ratio': crop_ratio,
        'detection_confidence': best_detection['confidence']
    }


def compare_results(gui_result, batch_result):
    """Compare GUI and batch processing results."""
    print("\n=== Comparison Results ===")
    
    if gui_result is None or batch_result is None:
        print("❌ One or both processing methods failed")
        return False
    
    # Compare key metrics
    confidence_match = abs(gui_result['confidence'] - batch_result['confidence']) < 0.001
    method_match = gui_result['method'] == batch_result['method']
    crop_ratio_match = abs(gui_result['crop_ratio'] - batch_result['crop_ratio']) < 0.001
    detection_match = abs(gui_result['detection_confidence'] - batch_result['detection_confidence']) < 0.001
    
    # Compare image dimensions
    gui_shape = gui_result['image'].shape[:2]
    batch_shape = batch_result['image'].shape[:2]
    dimensions_match = gui_shape == batch_shape
    
    print(f"Confidence match: {'✓' if confidence_match else '❌'} (GUI: {gui_result['confidence']:.3f}, Batch: {batch_result['confidence']:.3f})")
    print(f"Method match: {'✓' if method_match else '❌'} (GUI: {gui_result['method']}, Batch: {batch_result['method']})")
    print(f"Crop ratio match: {'✓' if crop_ratio_match else '❌'} (GUI: {gui_result['crop_ratio']:.3f}, Batch: {batch_result['crop_ratio']:.3f})")
    print(f"Detection confidence match: {'✓' if detection_match else '❌'} (GUI: {gui_result['detection_confidence']:.3f}, Batch: {batch_result['detection_confidence']:.3f})")
    print(f"Dimensions match: {'✓' if dimensions_match else '❌'} (GUI: {gui_shape}, Batch: {batch_shape})")
    
    all_match = confidence_match and method_match and crop_ratio_match and detection_match and dimensions_match
    
    if all_match:
        print("\n🎉 SUCCESS: GUI and batch processing produce identical results!")
    else:
        print("\n❌ FAILURE: GUI and batch processing produce different results!")
    
    return all_match


def main():
    """Main test function."""
    print("Testing GUI and Batch Processing Consistency")
    print("=" * 50)
    
    # Load configuration
    config = Config()
    
    print(f"Configuration settings:")
    print(f"  - Centering method: {config.get('centering.method')}")
    print(f"  - Use crop centering: {config.use_crop_centering}")
    print(f"  - Target position: {config.target_position}")
    print(f"  - Face weight: {config.face_weight}")
    print(f"  - Chest weight: {config.chest_weight}")
    print(f"  - Hip weight: {config.hip_weight}")
    
    # Load test image
    test_image = load_test_image()
    print(f"\nTest image loaded: {test_image.shape}")
    
    # Run both processing methods
    gui_result = simulate_gui_processing(config, test_image)
    batch_result = simulate_batch_processing(config, test_image)
    
    # Compare results
    success = compare_results(gui_result, batch_result)
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
