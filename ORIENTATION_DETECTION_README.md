# Automatic Orientation Detection

This document describes the automatic orientation detection and correction feature in Photo Center.

## Overview

The orientation detection system automatically identifies when human subjects in photos are upside down or sideways and applies the appropriate rotation to correct their orientation. This ensures that people always appear with their heads above their bodies in the final processed images.

## How It Works

### Detection Methods

The system offers three detection methods:

1. **Head-Body Vector Analysis** (`head_body_vector`)
   - Analyzes the vector from body center (shoulders/hips) to head center (nose/eyes)
   - Determines if the head is positioned correctly above the body
   - Most reliable for clear portraits with visible face and body keypoints

2. **Shoulder Alignment** (`shoulder_alignment`)
   - Examines the angle of the shoulder line
   - Detects when shoulders are more vertical than horizontal
   - Useful when head keypoints are not clearly visible

3. **Combined Analysis** (`combined`)
   - Uses both head-body vector and shoulder alignment
   - Selects the method with higher confidence
   - Provides the most robust detection across different scenarios

### Rotation Detection

The system can detect and correct the following orientations:
- **90° clockwise**: Subject rotated to the right
- **180°**: Subject upside down
- **270° clockwise (90° counter-clockwise)**: Subject rotated to the left

### Confidence Scoring

Each detection includes a confidence score (0.0 to 1.0) indicating how certain the system is about the orientation. Rotations are only applied when confidence exceeds the configured threshold.

## Configuration

### Basic Settings

```yaml
orientation_detection:
  enabled: true  # Enable/disable orientation detection
  method: "head_body_vector"  # Detection method
  confidence_threshold: 0.7  # Minimum confidence to apply rotation
```

### Advanced Settings

```yaml
orientation_detection:
  head_body_angle_threshold: 45  # Degrees deviation for head-body vector
  shoulder_angle_threshold: 30   # Degrees deviation for shoulder alignment
  min_keypoints_required: 4      # Minimum keypoints needed for detection
  rotation_angles: [90, 180, 270]  # Allowed rotation angles
  prefer_minimal_rotation: true  # Prefer smaller rotations when ambiguous
```

## Usage

### Command Line Interface

Orientation detection is automatically applied when processing images:

```bash
# Process single image with orientation detection
uv run photo-center -i image.jpg -o corrected.jpg

# Batch process with orientation detection
uv run photo-center -i /photos --batch
```

### GUI Interface

The GUI provides controls for orientation detection in the Settings panel:

- **Auto Orientation**: Enable/disable orientation detection
- **Orientation Method**: Choose detection method
- **Orientation Confidence**: Set confidence threshold

### Programmatic Usage

```python
from photo_center.image_processing.orientation_detector import OrientationDetector
from photo_center.image_processing.raw_processor import RawProcessor

# Initialize components
orientation_detector = OrientationDetector(config)
raw_processor = RawProcessor(config)

# Detect orientation
orientation_result = orientation_detector.detect_orientation(image, detection)

# Apply correction if needed
if orientation_result.needs_rotation:
    corrected_image = orientation_detector.apply_rotation(image, orientation_result.rotation_angle)

# Or use the integrated pipeline
corrected_image, orientation_info = raw_processor.apply_orientation_correction(image, detection)
```

## Integration with Processing Pipeline

Orientation detection is seamlessly integrated into the photo processing pipeline:

1. **Load Image**: Image is loaded from file
2. **Human Detection**: Detect humans and extract keypoints
3. **Orientation Analysis**: Analyze subject orientation using keypoints
4. **Apply Rotation**: Rotate image if needed and confidence is sufficient
5. **Re-detect Humans**: Re-run human detection on corrected image for accurate centering
6. **Center Subject**: Apply centering using corrected image and updated detections

## Performance Considerations

- **Minimal Overhead**: Orientation detection adds minimal processing time
- **Keypoint Reuse**: Uses existing keypoints from human detection
- **Conditional Processing**: Only applies rotation when needed
- **Batch Efficiency**: Optimized for batch processing workflows

## Logging and Debugging

The system provides detailed logging for orientation decisions:

```
Applied 180° rotation (confidence: 0.85)
Orientation correction suggested but confidence too low: 0.65
```

Analysis details include:
- Head and body center coordinates
- Calculated angles and vectors
- Keypoint counts and quality
- Confidence calculation breakdown

## Testing

Use the provided test script to verify orientation detection:

```bash
python test_orientation.py
```

This script:
- Tests orientation detection on sample images
- Compares different detection methods
- Creates rotated test images for validation
- Provides detailed analysis output

## Troubleshooting

### Common Issues

1. **No Rotation Applied**
   - Check if orientation detection is enabled
   - Verify confidence threshold is not too high
   - Ensure sufficient keypoints are detected

2. **Incorrect Rotation**
   - Try different detection methods
   - Adjust angle thresholds
   - Check keypoint quality in logs

3. **Performance Issues**
   - Reduce minimum keypoints required
   - Use simpler detection methods
   - Consider disabling for batch processing if not needed

### Configuration Tips

- **For Portraits**: Use `head_body_vector` method
- **For Group Photos**: Use `combined` method
- **For Low Quality Images**: Lower confidence threshold
- **For High Precision**: Increase confidence threshold

## Future Enhancements

Potential improvements for future versions:
- Machine learning-based orientation detection
- Support for custom rotation angles
- Face orientation analysis
- Integration with EXIF orientation data
- Automatic quality assessment
