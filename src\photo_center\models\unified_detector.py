"""Unified human detector that manages multiple detection models."""

import numpy as np
from typing import List, Dict, Any, Optional

from ..utils.logger import get_logger
from ..utils.config import Config
from .model_manager import ModelManager


class UnifiedHumanDetector:
    """Unified interface for human detection using multiple models."""
    
    def __init__(self, config: Optional[Config] = None):
        """Initialize unified human detector.
        
        Args:
            config: Configuration object. If None, creates default config.
        """
        self.config = config or Config()
        self.logger = get_logger(__name__)
        self.model_manager = ModelManager(self.config)
        
        self.logger.info("Unified human detector initialized")
    
    def detect_humans(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """Detect humans in image using the configured model.
        
        Args:
            image: Input image as numpy array (BGR format)
            
        Returns:
            List of detection dictionaries containing:
            - bbox: [x1, y1, x2, y2] bounding box coordinates
            - confidence: Detection confidence score
            - keypoints: Dictionary of keypoint coordinates
            - center: Center point of the detection
        """
        try:
            detections = self.model_manager.detect_humans(image)
            
            # Log detection results
            model_type = self.model_manager.get_current_model_type()
            self.logger.debug(f"Detected {len(detections)} humans using {model_type.value}")
            
            return detections
            
        except Exception as e:
            self.logger.error(f"Error in human detection: {e}")
            return []
    
    def get_best_detection(self, detections: List[Dict[str, Any]], image_shape: Optional[tuple] = None) -> Dict[str, Any]:
        """Get the best detection from a list of detections.
        
        For graduation photos, this will prioritize the most centered person.
        
        Args:
            detections: List of detection dictionaries
            image_shape: Optional image shape (height, width) for better center calculation
            
        Returns:
            Best detection dictionary
        """
        if not detections:
            raise ValueError("No detections provided")
        
        return self.model_manager.get_best_detection(detections, image_shape)

    def get_two_best_detections(self, detections: List[Dict[str, Any]], image_shape: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """Get the two best detections from a list of detections for two-person centering.

        Args:
            detections: List of detection dictionaries
            image_shape: Optional image shape (height, width) for better center calculation

        Returns:
            List of the two best detection dictionaries (or all detections if less than 2)
        """
        if not detections:
            return []

        return self.model_manager.get_two_best_detections(detections, image_shape)

    def get_current_model_type(self) -> str:
        """Get the currently active model type."""
        return self.model_manager.get_current_model_type().value
    
    def switch_model(self, model_type: str) -> None:
        """Switch to a different model type.
        
        Args:
            model_type: Model type to switch to ('yolo', 'openpose', 'auto')
        """
        self.model_manager.switch_model(model_type)
        self.logger.info(f"Switched to model: {model_type}")
    
    def get_available_models(self) -> List[str]:
        """Get list of available model types."""
        return self.model_manager.get_available_models()
    
    def is_graduation_photo_mode(self) -> bool:
        """Check if graduation photo mode is enabled."""
        return self.config.graduation_photo_mode
