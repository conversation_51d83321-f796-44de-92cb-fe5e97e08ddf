#!/usr/bin/env python3
"""
Test script for orientation detection functionality.

This script tests the automatic orientation detection and correction
on a sample image to verify the implementation works correctly.
"""

import sys
import cv2
import numpy as np
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from photo_center.utils.config import Config
from photo_center.utils.logger import get_logger
from photo_center.models.unified_detector import UnifiedHumanDetector
from photo_center.image_processing.raw_processor import RawProcessor
from photo_center.image_processing.orientation_detector import OrientationDetector


def test_orientation_detection():
    """Test orientation detection on a sample image."""
    logger = get_logger(__name__)
    
    # Initialize components
    config = Config()
    human_detector = UnifiedHumanDetector(config)
    raw_processor = RawProcessor(config)
    orientation_detector = OrientationDetector(config)
    
    # Test image path
    test_image_path = Path("./test_photos/_SPC5194.NEF")
    
    if not test_image_path.exists():
        print(f"Test image not found: {test_image_path}")
        print("Please ensure the test image exists or update the path.")
        return
    
    try:
        # Load image
        print(f"Loading test image: {test_image_path}")
        image = raw_processor.load_image(test_image_path)
        print(f"Image loaded: {image.shape}, dtype: {image.dtype}")
        
        # Detect humans
        print("Detecting humans...")
        detections = human_detector.detect_humans(image)
        
        if not detections:
            print("No humans detected in the image.")
            return
        
        print(f"Found {len(detections)} human(s)")
        
        # Get best detection
        best_detection = human_detector.get_best_detection(detections)
        print(f"Best detection confidence: {best_detection['confidence']:.3f}")
        
        # Test orientation detection
        print("\nTesting orientation detection...")
        orientation_result = orientation_detector.detect_orientation(image, best_detection)
        
        print(f"Orientation analysis:")
        print(f"  Needs rotation: {orientation_result.needs_rotation}")
        print(f"  Rotation angle: {orientation_result.rotation_angle}°")
        print(f"  Confidence: {orientation_result.confidence:.3f}")
        print(f"  Method used: {orientation_result.method_used}")
        print(f"  Analysis details: {orientation_result.analysis_details}")
        
        # Test the full orientation correction pipeline
        print("\nTesting full orientation correction pipeline...")
        corrected_image, orientation_info = raw_processor.apply_orientation_correction(image, best_detection)
        
        print(f"Orientation correction result:")
        print(f"  Applied: {orientation_info['applied']}")
        print(f"  Rotation angle: {orientation_info['rotation_angle']}°")
        print(f"  Confidence: {orientation_info['confidence']:.3f}")
        
        if orientation_info['applied']:
            print(f"Image rotated by {orientation_info['rotation_angle']}°")
            print(f"Original shape: {image.shape}")
            print(f"Corrected shape: {corrected_image.shape}")
            
            # Save corrected image for visual inspection
            output_path = Path("test_orientation_corrected.jpg")
            raw_processor.save_image(corrected_image, output_path)
            print(f"Corrected image saved to: {output_path}")
        else:
            print("No rotation was applied")
        
        # Test different orientation methods
        print("\nTesting different orientation detection methods...")
        
        methods = ['head_body_vector', 'shoulder_alignment', 'combined']
        for method in methods:
            config.set('orientation_detection.method', method)
            test_detector = OrientationDetector(config)
            result = test_detector.detect_orientation(image, best_detection)
            
            print(f"  {method}: rotation={result.rotation_angle}°, confidence={result.confidence:.3f}")
        
        print("\nOrientation detection test completed successfully!")
        
    except Exception as e:
        logger.error(f"Error during orientation detection test: {e}")
        print(f"Error: {e}")
        return False
    
    return True


def create_test_rotated_image():
    """Create a rotated test image to verify detection works."""
    test_image_path = Path("./test_photos/_SPC5194.NEF")
    
    if not test_image_path.exists():
        print(f"Test image not found: {test_image_path}")
        return
    
    try:
        config = Config()
        raw_processor = RawProcessor(config)
        
        # Load original image
        image = raw_processor.load_image(test_image_path)
        
        # Create rotated versions
        rotations = [90, 180, 270]
        
        for angle in rotations:
            if angle == 90:
                rotated = cv2.rotate(image, cv2.ROTATE_90_CLOCKWISE)
            elif angle == 180:
                rotated = cv2.rotate(image, cv2.ROTATE_180)
            elif angle == 270:
                rotated = cv2.rotate(image, cv2.ROTATE_90_COUNTERCLOCKWISE)
            
            output_path = Path(f"test_rotated_{angle}.jpg")
            raw_processor.save_image(rotated, output_path)
            print(f"Created rotated test image: {output_path}")
        
        print("Test rotated images created successfully!")
        
    except Exception as e:
        print(f"Error creating test images: {e}")


if __name__ == "__main__":
    print("Photo Center - Orientation Detection Test")
    print("=" * 50)
    
    # Test orientation detection
    success = test_orientation_detection()
    
    if success:
        print("\n" + "=" * 50)
        print("All tests passed! Orientation detection is working correctly.")
        
        # Optionally create test rotated images
        create_rotated = input("\nCreate rotated test images? (y/n): ").lower().strip()
        if create_rotated == 'y':
            create_test_rotated_image()
    else:
        print("\n" + "=" * 50)
        print("Tests failed. Please check the error messages above.")
        sys.exit(1)
