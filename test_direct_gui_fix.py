#!/usr/bin/env python3
"""
Test script to verify the direct GUI fix is working.
"""

import sys
import numpy as np
import cv2
from pathlib import Path

# Add src to path for imports
current_dir = Path(__file__).parent
src_path = current_dir / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def test_direct_gui_fix():
    """Test the direct GUI fix with embedded colors."""
    print("🔧 Testing Direct GUI Fix")
    print("=" * 40)
    
    try:
        from PySide6.QtWidgets import QApplication
        from photo_center.ui.preview_widget import PreviewWidget
        from photo_center.image_processing.centering import CenteringResult
        
        # Create QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Create preview widget
        preview_widget = PreviewWidget()
        
        # Create test data
        test_image = np.zeros((400, 600, 3), dtype=np.uint8)
        test_image[100:300, 150:450] = (128, 128, 128)  # Gray rectangle
        
        mock_detection = {
            'bbox': [150, 100, 450, 300],
            'confidence': 0.85,
            'keypoints': {
                'nose': (300, 150),
                'left_eye': (280, 140),
                'right_eye': (320, 140),
                'left_shoulder': (250, 200),
                'right_shoulder': (350, 200)
            }
        }
        
        mock_result = CenteringResult(
            cropped_image=test_image[50:350, 100:500],
            crop_box=(100, 50, 500, 350),
            subject_center=(300, 200),
            target_center=(200, 150),
            confidence=0.75,
            method_used="face_chest_based"
        )
        
        # Set up the preview widget
        preview_widget.set_original_image(test_image)
        preview_widget.set_processed_image(test_image, mock_detection, mock_result)
        
        print("✓ Preview widget configured")
        
        # Test show_detection method
        print("\n🎨 Testing show_detection with direct colors...")
        
        # Capture the visualization by monkey-patching the set_image method
        captured_image = None
        original_set_image = preview_widget.image_label.set_image
        
        def capture_image(image):
            nonlocal captured_image
            captured_image = image.copy()
            return original_set_image(image)
        
        preview_widget.image_label.set_image = capture_image
        
        # Call the method
        preview_widget.show_detection()
        
        # Restore original method
        preview_widget.image_label.set_image = original_set_image
        
        if captured_image is not None:
            # Save the captured image
            cv2.imwrite("direct_gui_detection_test.png", captured_image)
            print("✓ Detection visualization captured and saved")
            
            # Analyze colors
            magenta_pixels = np.sum(np.all(captured_image == [255, 0, 255], axis=2))
            green_pixels = np.sum(np.all(captured_image == [0, 255, 0], axis=2))
            cyan_pixels = np.sum(np.all(captured_image == [0, 255, 255], axis=2))
            hot_pink_pixels = np.sum(np.all(captured_image == [255, 20, 147], axis=2))
            
            print(f"\n📊 Color Analysis:")
            print(f"   Magenta pixels (detection box): {magenta_pixels}")
            print(f"   Green pixels (crop area): {green_pixels}")
            print(f"   Cyan pixels (subject center): {cyan_pixels}")
            print(f"   Hot Pink pixels (face keypoints): {hot_pink_pixels}")
            
            if magenta_pixels > 0:
                print("✅ BRIGHT MAGENTA detection box found!")
            else:
                print("❌ No magenta detection box found")
                
            if green_pixels > 0:
                print("✅ BRIGHT GREEN crop area found!")
            else:
                print("❌ No green crop area found")
                
            if hot_pink_pixels > 0:
                print("✅ HOT PINK face keypoints found!")
            else:
                print("❌ No hot pink face keypoints found")
        
        # Test show_visualization method
        print("\n🎨 Testing show_visualization with direct colors...")
        
        captured_image = None
        preview_widget.image_label.set_image = capture_image
        
        # Call the method
        preview_widget.show_visualization()
        
        # Restore original method
        preview_widget.image_label.set_image = original_set_image
        
        if captured_image is not None:
            # Save the captured image
            cv2.imwrite("direct_gui_visualization_test.png", captured_image)
            print("✓ Visualization captured and saved")
            
            # Analyze colors
            green_target_pixels = np.sum(np.all(captured_image == [0, 255, 0], axis=2))
            
            print(f"\n📊 Visualization Color Analysis:")
            print(f"   Green pixels (target center): {green_target_pixels}")
            
            if green_target_pixels > 0:
                print("✅ BRIGHT GREEN target center found!")
            else:
                print("❌ No green target center found")
        
        # Clean up
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing direct GUI fix: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎯 Testing Direct GUI Color Fix")
    print("=" * 50)
    
    if test_direct_gui_fix():
        print("\n🎉 Direct GUI fix test completed!")
        print("\n💡 The GUI methods now have bright colors embedded directly.")
        print("💡 Restart your GUI application to see the changes!")
        print("💡 Check the saved test images to verify colors are working.")
    else:
        print("\n❌ Direct GUI fix test failed")
        sys.exit(1)
