#!/usr/bin/env python3
"""
Debug script to check what's happening with GUI colors.
"""

import sys
import numpy as np
import cv2
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def debug_preview_widget_methods():
    """Debug the actual preview widget methods."""
    print("🔍 Debugging Preview Widget Methods")
    print("=" * 50)
    
    try:
        # Import exactly like the main GUI does
        from src.photo_center.ui.preview_widget import PreviewWidget
        from src.photo_center.image_processing.centering import CenteringResult
        
        print("✓ Imported PreviewWidget from src.photo_center.ui.preview_widget")
        
        # Check the actual method source code
        import inspect
        
        print("\n📋 show_detection method source:")
        print("-" * 30)
        show_detection_source = inspect.getsource(PreviewWidget.show_detection)
        print(show_detection_source[:500] + "..." if len(show_detection_source) > 500 else show_detection_source)
        
        print("\n📋 show_visualization method source:")
        print("-" * 30)
        show_visualization_source = inspect.getsource(PreviewWidget.show_visualization)
        print(show_visualization_source[:500] + "..." if len(show_visualization_source) > 500 else show_visualization_source)
        
        # Check if the methods contain the bright colors
        if "(255, 0, 255)" in show_detection_source:
            print("✅ show_detection contains bright magenta (255, 0, 255)")
        else:
            print("❌ show_detection does NOT contain bright magenta")
            
        if "(255, 20, 147)" in show_detection_source:
            print("✅ show_detection contains hot pink (255, 20, 147)")
        else:
            print("❌ show_detection does NOT contain hot pink")
            
        if "(0, 255, 0)" in show_visualization_source:
            print("✅ show_visualization contains bright green (0, 255, 0)")
        else:
            print("❌ show_visualization does NOT contain bright green")
        
        return True
        
    except Exception as e:
        print(f"❌ Error debugging preview widget: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_module_locations():
    """Debug where modules are being loaded from."""
    print("\n🗂️ Debugging Module Locations")
    print("=" * 40)
    
    try:
        # Check where preview_widget is loaded from
        from src.photo_center.ui import preview_widget
        print(f"preview_widget module location: {preview_widget.__file__}")
        
        # Check where centering is loaded from
        from src.photo_center.image_processing import centering
        print(f"centering module location: {centering.__file__}")
        
        # Check the actual PhotoCenterer class
        from src.photo_center.image_processing.centering import PhotoCenterer
        import inspect
        
        visualize_method = PhotoCenterer.visualize_original_detection
        method_source = inspect.getsource(visualize_method)
        
        if "(255, 0, 255)" in method_source:
            print("✅ PhotoCenterer.visualize_original_detection contains bright magenta")
        else:
            print("❌ PhotoCenterer.visualize_original_detection does NOT contain bright magenta")
        
        return True
        
    except Exception as e:
        print(f"❌ Error debugging module locations: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_actual_gui_execution():
    """Test what happens when we actually run the GUI methods."""
    print("\n🖥️ Testing Actual GUI Execution")
    print("=" * 40)
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.photo_center.ui.preview_widget import PreviewWidget
        from src.photo_center.image_processing.centering import CenteringResult
        
        # Create QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Create preview widget
        preview_widget = PreviewWidget()
        
        # Create test data
        test_image = np.zeros((400, 600, 3), dtype=np.uint8)
        test_image[100:300, 150:450] = (128, 128, 128)
        
        mock_detection = {
            'bbox': [150, 100, 450, 300],
            'confidence': 0.85,
            'keypoints': {
                'nose': (300, 150),
                'left_eye': (280, 140),
                'right_eye': (320, 140)
            }
        }
        
        mock_result = CenteringResult(
            cropped_image=test_image[50:350, 100:500],
            crop_box=(100, 50, 500, 350),
            subject_center=(300, 200),
            target_center=(200, 150),
            confidence=0.75,
            method_used="face_chest_based"
        )
        
        # Set up the preview widget
        preview_widget.set_original_image(test_image)
        preview_widget.set_processed_image(test_image, mock_detection, mock_result)
        
        print("✓ Preview widget set up with test data")
        
        # Monkey patch to capture what actually gets displayed
        captured_images = []
        original_set_image = preview_widget.image_label.set_image
        
        def capture_set_image(image):
            captured_images.append(image.copy())
            print(f"📸 Captured image: {image.shape}, dtype: {image.dtype}")
            
            # Quick color analysis
            unique_colors = np.unique(image.reshape(-1, 3), axis=0)
            print(f"   Unique colors found: {len(unique_colors)}")
            
            # Check for specific bright colors
            magenta_pixels = np.sum(np.all(image == [255, 0, 255], axis=2))
            green_pixels = np.sum(np.all(image == [0, 255, 0], axis=2))
            
            if magenta_pixels > 0:
                print(f"   ✅ Found {magenta_pixels} BRIGHT MAGENTA pixels!")
            if green_pixels > 0:
                print(f"   ✅ Found {green_pixels} BRIGHT GREEN pixels!")
            
            if magenta_pixels == 0 and green_pixels == 0:
                print("   ❌ No bright colors found - still showing black!")
            
            return original_set_image(image)
        
        preview_widget.image_label.set_image = capture_set_image
        
        # Test show_detection
        print("\n🎨 Calling show_detection()...")
        preview_widget.show_detection()
        
        # Test show_visualization  
        print("\n🎨 Calling show_visualization()...")
        preview_widget.show_visualization()
        
        # Save captured images for inspection
        for i, img in enumerate(captured_images):
            filename = f"debug_captured_image_{i}.png"
            cv2.imwrite(filename, img)
            print(f"💾 Saved captured image: {filename}")
        
        # Clean up
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing GUI execution: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🐛 GUI Color Debugging")
    print("=" * 60)
    
    success = True
    
    # Debug preview widget methods
    if not debug_preview_widget_methods():
        success = False
    
    # Debug module locations
    if not debug_module_locations():
        success = False
    
    # Test actual GUI execution
    if not test_actual_gui_execution():
        success = False
    
    if success:
        print("\n🎉 Debugging completed!")
        print("💡 Check the output above to see what's happening with colors.")
    else:
        print("\n❌ Some debugging steps failed")
        sys.exit(1)
