#!/usr/bin/env python3
"""
Test script to verify GUI visualization colors are working correctly.
"""

import sys
import numpy as np
import cv2
from pathlib import Path

# Add src to path for imports
current_dir = Path(__file__).parent
src_path = current_dir / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def test_gui_visualization_colors():
    """Test the actual GUI visualization methods with new colors."""
    print("🎨 Testing GUI Visualization Colors")
    print("=" * 50)
    
    try:
        from photo_center.image_processing.centering import PhotoCenterer, CenteringResult
        from photo_center.utils.config import Config
        
        # Initialize components
        config = Config()
        centerer = PhotoCenterer(config)
        
        # Create test image
        test_image = np.zeros((400, 600, 3), dtype=np.uint8)
        test_image[100:300, 150:450] = (128, 128, 128)  # Gray rectangle
        
        # Create mock detection data
        mock_detection = {
            'bbox': [150, 100, 450, 300],
            'confidence': 0.85,
            'keypoints': {
                'nose': (300, 150),
                'left_eye': (280, 140),
                'right_eye': (320, 140),
                'left_shoulder': (250, 200),
                'right_shoulder': (350, 200)
            }
        }
        
        # Create mock centering result
        mock_result = CenteringResult(
            cropped_image=test_image[50:350, 100:500],
            crop_box=(100, 50, 500, 350),
            subject_center=(300, 200),
            target_center=(200, 150),
            confidence=0.75,
            method_used="face_chest_based"
        )
        
        print("✓ Test data created")
        
        # Test the GUI visualization method
        print("\n🔍 Testing visualize_original_detection method...")
        vis_image = centerer.visualize_original_detection(test_image, mock_detection, mock_result)
        
        # Save the visualization
        output_path = "gui_visualization_test.png"
        cv2.imwrite(output_path, vis_image)
        print(f"✓ Visualization saved to: {output_path}")
        
        # Check if the image has the expected colors
        # Look for magenta pixels (detection box)
        magenta_mask = np.all(vis_image == [255, 0, 255], axis=2)
        magenta_pixels = np.sum(magenta_mask)
        
        # Look for green pixels (crop area)
        green_mask = np.all(vis_image == [0, 255, 0], axis=2)
        green_pixels = np.sum(green_mask)
        
        # Look for cyan pixels (subject center)
        cyan_mask = np.all(vis_image == [0, 255, 255], axis=2)
        cyan_pixels = np.sum(cyan_mask)
        
        print(f"\n📊 Color Analysis:")
        print(f"   Magenta pixels (detection box): {magenta_pixels}")
        print(f"   Green pixels (crop area): {green_pixels}")
        print(f"   Cyan pixels (subject center): {cyan_pixels}")
        
        if magenta_pixels > 0:
            print("✅ Magenta detection box found!")
        else:
            print("❌ No magenta detection box found")
            
        if green_pixels > 0:
            print("✅ Green crop area found!")
        else:
            print("❌ No green crop area found")
            
        if cyan_pixels > 0:
            print("✅ Cyan subject center found!")
        else:
            print("❌ No cyan subject center found")
        
        # Test centering visualization
        print("\n🔍 Testing visualize_centering method...")
        centering_vis = centerer.visualize_centering(mock_result)
        
        # Save centering visualization
        centering_output = "centering_visualization_test.png"
        cv2.imwrite(centering_output, centering_vis)
        print(f"✓ Centering visualization saved to: {centering_output}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing GUI visualization: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_human_detector_visualization():
    """Test the human detector visualization method."""
    print("\n🤖 Testing Human Detector Visualization")
    print("=" * 50)
    
    try:
        from photo_center.models.human_detector import HumanDetector
        from photo_center.utils.config import Config
        
        # Initialize detector
        config = Config()
        detector = HumanDetector(config)
        
        # Create test image
        test_image = np.zeros((400, 600, 3), dtype=np.uint8)
        test_image[100:300, 150:450] = (128, 128, 128)  # Gray rectangle
        
        # Create mock detections
        mock_detections = [
            {
                'bbox': [150, 100, 450, 300],
                'confidence': 0.85,
                'keypoints': {
                    'nose': (300, 150),
                    'left_eye': (280, 140),
                    'right_eye': (320, 140)
                }
            }
        ]
        
        # Test visualization
        vis_image = detector.visualize_detections(test_image, mock_detections)
        
        # Save the visualization
        output_path = "detector_visualization_test.png"
        cv2.imwrite(output_path, vis_image)
        print(f"✓ Detector visualization saved to: {output_path}")
        
        # Check for bright colors
        magenta_mask = np.all(vis_image == [255, 0, 255], axis=2)
        magenta_pixels = np.sum(magenta_mask)
        
        hot_pink_mask = np.all(vis_image == [255, 20, 147], axis=2)
        hot_pink_pixels = np.sum(hot_pink_mask)
        
        print(f"\n📊 Detector Color Analysis:")
        print(f"   Magenta pixels (detection box): {magenta_pixels}")
        print(f"   Hot Pink pixels (face keypoints): {hot_pink_pixels}")
        
        if magenta_pixels > 0:
            print("✅ Bright magenta detection box found!")
        else:
            print("❌ No bright magenta detection box found")
            
        if hot_pink_pixels > 0:
            print("✅ Hot pink face keypoints found!")
        else:
            print("❌ No hot pink face keypoints found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing detector visualization: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎯 Testing GUI Visualization Colors")
    print("=" * 60)
    
    success = True
    
    # Test GUI visualization
    if not test_gui_visualization_colors():
        success = False
    
    # Test detector visualization
    if not test_human_detector_visualization():
        success = False
    
    if success:
        print("\n🎉 All GUI visualization tests passed!")
        print("The new bright colors should be visible in the saved test images.")
        print("If GUI still shows black boxes, there may be a caching or refresh issue.")
    else:
        print("\n❌ Some visualization tests failed")
        sys.exit(1)
